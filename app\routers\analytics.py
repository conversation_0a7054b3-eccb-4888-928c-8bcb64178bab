from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from ..db import get_db
from ..dependencies import get_current_user
from ..schemas import Progress<PERSON>hartResponse
from ..utils import to_object_id

router = APIRouter(prefix="/analytics", tags=["analytics"])


async def _ensure_can_access_client(client: Dict[str, Any], user: Dict[str, Any]):
    if not client:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")
    role = user.get("role")
    if role == "parent":
        allowed = user["_id"] in client.get("parent_ids", [])
        if not allowed:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")
    else:  # therapist
        if client.get("therapist_id") != user["_id"]:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")


@router.get("/clients/{client_id}/progress-chart", response_model=ProgressChartResponse)
async def progress_chart(client_id: str, current_user: Dict[str, Any] = Depends(get_current_user)):
    db = get_db()
    oid = to_object_id(client_id)
    client = await db.clients.find_one({"_id": oid})
    await _ensure_can_access_client(client, current_user)

    cursor = db.progress.find({"client_id": oid}).sort("date", 1)
    docs = await cursor.to_list(1000)

    dates: List = [d.get("date") for d in docs]
    # Collect all metric keys
    metric_keys = set()
    for d in docs:
        metric_keys.update((d.get("metrics") or {}).keys())

    series: Dict[str, List[float]] = {}
    for key in sorted(metric_keys):
        values: List[float] = []
        for d in docs:
            metrics = d.get("metrics") or {}
            v = metrics.get(key)
            if v is None:
                values.append(0.0)
            else:
                try:
                    values.append(float(v))
                except Exception:
                    values.append(0.0)
        series[key] = values

    return ProgressChartResponse(client_id=client_id, dates=dates, series=series)

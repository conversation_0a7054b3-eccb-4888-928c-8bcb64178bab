from contextlib import asynccontextmanager
from fastapi import FastAPI, status
from fastapi.responses import JSONResponse
from app.db import connect_to_mongo, close_mongo_connection, get_db
from app.routers import (
    auth as auth_router,
    clients as clients_router,
    sessions as sessions_router,
    analytics as analytics_router,
    admin as admin_router,
    users as users_router,
)
from app.config import settings
from app.middleware import ConcurrencyLimitMiddleware, BodySizeLimitMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    await connect_to_mongo()
    try:
        yield
    finally:
        await close_mongo_connection()


app = FastAPI(title="ABA Client Portal API", version="0.1.0", lifespan=lifespan)
# Add conservative safeguards for resource usage
app.add_middleware(ConcurrencyLimitMiddleware, max_concurrency=settings.MAX_CONCURRENCY)
app.add_middleware(BodySizeLimitMiddleware, max_body_size=settings.MAX_BODY_SIZE_BYTES)


# Overall health endpoint
@app.get("/health")
async def health():
    try:
        # DB check
        db = get_db()
        res = await db.command("ping")
        db_ok = res.get("ok") == 1
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "app": "running",
                "database": "disconnected",
                "error": str(e),
            },
        )

    if db_ok:
        return {"status": "healthy", "app": "running", "database": "connected"}

    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            "status": "unhealthy",
            "app": "running",
            "database": "disconnected",
            "error": f"Unexpected ping response: {res}",
        },
    )


# Health/root endpoint
@app.get("/")
async def root():
    return {"message": "ABA Client Portal API is running"}


@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}

# Database health endpoint
@app.get("/health/db")
async def health_db():
    try:
        db = get_db()
        # Perform a ping command to verify connectivity
        res = await db.command("ping")
        if res.get("ok") == 1:
            return {"status": "healthy", "database": "connected"}
        # Unexpected response shape
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "error": f"Unexpected ping response: {res}",
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e),
            },
        )



# Include feature routers
app.include_router(auth_router.router)
app.include_router(clients_router.router)
app.include_router(sessions_router.router)
app.include_router(analytics_router.router)
app.include_router(admin_router.router)
app.include_router(users_router.router)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)

from fastapi import APIRouter, HTTPException, Depends, status

from ..db import get_db
from ..dependencies import get_current_user
from ..schemas import LoginRequest, TokenResponse, UserMe
from ..security import verify_password, create_access_token
from ..utils import convert_object_ids

router = APIRouter(prefix="/auth", tags=["auth"])


@router.post("/login", response_model=TokenResponse)
async def login(payload: LoginRequest):
    db = get_db()
    user = await db.users.find_one({"email": payload.email})
    if not user or not verify_password(payload.password, user.get("password_hash", ""), user.get("salt", "")):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
    token = create_access_token({"sub": str(user["_id"]), "role": user.get("role")})
    return TokenResponse(access_token=token)


@router.get("/me", response_model=UserMe)
async def me(current_user: dict = Depends(get_current_user)):
    safe = convert_object_ids({
        "_id": current_user["_id"],
        "email": current_user.get("email"),
        "full_name": current_user.get("full_name"),
        "role": current_user.get("role"),
        "client_ids": current_user.get("client_ids", []),
    })
    return safe

from __future__ import annotations

from typing import Callable, Awaitable, Dict, Any
import anyio


class ConcurrencyLimitMiddleware:
    """
    ASGI middleware to limit the number of concurrent HTTP requests handled by
    this process. Uses anyio.Semaphore so it works with asyncio or trio backends.
    """

    def __init__(self, app: Callable, max_concurrency: int = 100) -> None:
        self.app = app
        # Ensure max_concurrency is at least 1
        self._sem = anyio.Semaphore(max(1, int(max_concurrency)))

    async def __call__(self, scope: Dict[str, Any], receive: Callable[[], Awaitable[Dict]], send: Callable[[Dict], Awaitable[None]]):
        if scope.get("type") != "http":
            await self.app(scope, receive, send)
            return
        async with self._sem:
            await self.app(scope, receive, send)


class BodySizeLimitMiddleware:
    """
    ASGI middleware that rejects requests with a Content-Length exceeding
    the configured limit. This is a conservative safeguard against very
    large uploads. Note: streaming/chunked requests without Content-Length
    are not actively limited by this simple middleware.
    """

    def __init__(self, app: Callable, max_body_size: int = 10 * 1024 * 1024) -> None:
        self.app = app
        self._limit = max(0, int(max_body_size))

    async def __call__(self, scope: Dict[str, Any], receive: Callable[[], Awaitable[Dict]], send: Callable[[Dict], Awaitable[None]]):
        if scope.get("type") != "http":
            await self.app(scope, receive, send)
            return

        headers = dict((k.lower(), v) for k, v in scope.get("headers", []))
        content_length = headers.get(b"content-length")
        if content_length is not None:
            try:
                length = int(content_length.decode("latin-1"))
            except Exception:
                length = None
            if length is not None and length > self._limit:
                # 413 Payload Too Large
                await send({
                    "type": "http.response.start",
                    "status": 413,
                    "headers": [(b"content-type", b"text/plain; charset=utf-8")],
                })
                await send({
                    "type": "http.response.body",
                    "body": b"Request body too large",
                    "more_body": False,
                })
                return

        await self.app(scope, receive, send)


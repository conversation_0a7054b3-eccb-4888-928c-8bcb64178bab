from typing import List, Dict, Any
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status
from bson import ObjectId
from ..db import get_db
from ..dependencies import get_current_user, require_role
from ..schemas import SessionCreate, SessionOut
from ..utils import to_object_id, convert_object_ids

router = APIRouter(prefix="/sessions", tags=["sessions"])


async def _can_access_client(client: Dict[str, Any], user: Dict[str, Any]) -> bool:
    if not client:
        return False
    role = user.get("role")
    uid = user["_id"]
    if role == "parent":
        return uid in client.get("parent_ids", [])
    else:
        return client.get("therapist_id") == uid


@router.post("", response_model=SessionOut)
async def create_session(payload: SessionCreate, therapist: Dict[str, Any] = Depends(require_role("therapist"))):
    db = get_db()
    client_oid = to_object_id(payload.client_id)
    client = await db.clients.find_one({"_id": client_oid})
    if not await _can_access_client(client, therapist):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")

    dt = payload.date or datetime.now(timezone.utc)
    doc = {
        "client_id": client_oid,
        "date": dt,
        "created_by": therapist["_id"],
        "notes": payload.notes,
        "goals": payload.goals or [],
        "metrics": payload.metrics or {},
    }
    result = await db.sessions.insert_one(doc)
    created = await db.sessions.find_one({"_id": result.inserted_id})

    # Optionally, record progress point if metrics provided
    if payload.metrics:
        await db.progress.insert_one({
            "client_id": client_oid,
            "date": dt.date(),
            "metrics": payload.metrics,
        })

    safe = convert_object_ids(created)
    safe["_id"] = str(created["_id"])
    safe["client_id"] = str(created["client_id"])
    safe["created_by"] = str(created["created_by"])
    return safe


@router.get("", response_model=List[SessionOut])
async def list_sessions(current_user: Dict[str, Any] = Depends(get_current_user)):
    db = get_db()
    role = current_user.get("role")
    if role == "parent":
        client_ids = [ObjectId(cid) for cid in current_user.get("client_ids", [])]
        cursor = db.sessions.find({"client_id": {"$in": client_ids}}).sort("date", -1)
    else:
        # Get clients for which the therapist is responsible
        client_ids = [c["_id"] async for c in db.clients.find({"therapist_id": current_user["_id"]}, {"_id": 1})]
        cursor = db.sessions.find({"client_id": {"$in": client_ids}}).sort("date", -1)
    docs = await cursor.to_list(1000)
    out: List[Dict[str, Any]] = []
    for d in docs:
        safe = convert_object_ids(d)
        safe["_id"] = str(d["_id"]) 
        safe["client_id"] = str(d["client_id"]) 
        safe["created_by"] = str(d["created_by"]) 
        out.append(safe)
    return out


@router.get("/{session_id}", response_model=SessionOut)
async def get_session(session_id: str, current_user: Dict[str, Any] = Depends(get_current_user)):
    db = get_db()
    sid = to_object_id(session_id)
    session = await db.sessions.find_one({"_id": sid})
    if not session:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")

    client = await db.clients.find_one({"_id": session["client_id"]})
    if not await _can_access_client(client, current_user):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")

    safe = convert_object_ids(session)
    safe["_id"] = str(session["_id"])
    safe["client_id"] = str(session["client_id"])
    safe["created_by"] = str(session["created_by"])
    return safe

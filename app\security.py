import secrets
import hashlib
from datetime import datetime, timedelta, timezone
from typing import <PERSON><PERSON>, Dict, Any
import jwt
from .config import settings


def hash_password(password: str, salt: bytes | None = None) -> Tuple[str, str]:
    if salt is None:
        salt = secrets.token_bytes(16)
    if isinstance(salt, str):
        # accept hex string
        salt_bytes = bytes.fromhex(salt)
    else:
        salt_bytes = salt
    h = hashlib.sha256()
    h.update(salt_bytes + password.encode("utf-8"))
    return h.hexdigest(), salt_bytes.hex()


def verify_password(password: str, stored_hash: str, salt_hex: str) -> bool:
    computed, _ = hash_password(password, salt_hex)
    return secrets.compare_digest(computed, stored_hash)


def create_access_token(data: Dict[str, Any], expires_delta: timedelta | None = None) -> str:
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (expires_delta or settings.access_token_expires)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def decode_token(token: str) -> Dict[str, Any]:
    return jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])

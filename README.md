# ABA Client Portal API

This is a FastAPI-based service.

Quick start:
- Requires Python 3.12 and virtualenv (or the built-in venv module).
- Uses pip for dependencies.
- Runs with Uvicorn ASGI server.

## 1) <PERSON><PERSON> and enter the project
- git clone <your-repo-url>
- cd <your-project-folder>

## 2) Create and activate a virtual environment
- macOS/Linux:
  - python3.12 -m venv .venv
  - source .venv/bin/activate
- Windows (PowerShell):
  - py -3.12 -m venv .venv
  - .\.venv\Scripts\Activate.ps1

## 3) Install dependencies
install requirements.txt:
- pip install -r requirements.txt

## 4) Run the API server
- uvicorn main:app --reload --host 0.0.0.0 --port 8000

This starts the server on http://127.0.0.1:8000

Interactive API docs:
- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

## Database configuration
- By default, the app connects to a MongoDB Atlas cluster using this URI:
  mongodb+srv://admin:<EMAIL>/?retryWrites=true&w=majority&appName=weachaviaba
- The database name used is aba_client_portal.
- You can override the connection string and database name via environment variables:
  - MONGODB_URI
  - DB_NAME

TLS/SSL notes:
- The app now uses the certifi CA bundle for TLS verification when connecting to MongoDB Atlas. This helps avoid SSL handshake errors on Windows.
- If you are behind a corporate proxy/antivirus that intercepts TLS and you still see SSL handshake failures, you can temporarily disable certificate validation for development by setting:
  - TLS_ALLOW_INVALID_CERTS=1 (do NOT use this in production)

Example (PowerShell):
- $env:MONGODB_URI = "mongodb+srv://<user>:<password>@<cluster>/?retryWrites=true&w=majority"
- $env:DB_NAME = "my_database"
- $env:TLS_ALLOW_INVALID_CERTS = "1"  # optional; development only if you have TLS interception
- uvicorn main:app --reload



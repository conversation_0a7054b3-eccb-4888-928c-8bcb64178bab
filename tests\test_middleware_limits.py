import time
import anyio
import pytest
import httpx
from httpx import ASGITransport

from fastapi import FastAPI, Request
from fastapi.testclient import TestClient

from app.middleware import ConcurrencyLimitMiddleware, BodySizeLimitMiddleware


def build_body_limit_app(limit_bytes: int) -> TestClient:
    app = FastAPI()
    app.add_middleware(BodySizeLimitMiddleware, max_body_size=limit_bytes)

    @app.post("/echo-bytes")
    async def echo_bytes(req: Request):
        data = await req.body()
        return {"len": len(data)}

    return TestClient(app)


@pytest.mark.anyio
async def test_concurrency_limit_serializes_when_set_to_one_anyio():
    app = FastAPI()
    app.add_middleware(ConcurrencyLimitMiddleware, max_concurrency=1)

    @app.get("/sleep")
    async def sleep_ep():
        await anyio.sleep(0.25)
        return {"ok": True}

    transport = ASGITransport(app=app)
    async with httpx.AsyncClient(transport=transport, base_url="http://test") as client:
        t0 = time.perf_counter()
        results = {}

        async def do(name: str):
            results[name] = await client.get("/sleep")

        async with anyio.create_task_group() as tg:
            tg.start_soon(do, "a")
            tg.start_soon(do, "b")
        total_elapsed = time.perf_counter() - t0

    assert results["a"].status_code == 200 and results["b"].status_code == 200
    # With max_conc=1 and each request sleeping ~0.25s, total should be ~0.5s or more.
    assert total_elapsed >= 0.45, f"Expected serialized handling, got total_elapsed={total_elapsed:.3f}s"


def test_body_size_limit_rejects_large_payload():
    client = build_body_limit_app(limit_bytes=10)

    # 10 bytes should be allowed
    resp_ok = client.post("/echo-bytes", data=b"0123456789")
    assert resp_ok.status_code == 200
    assert resp_ok.json()["len"] == 10

    # 11 bytes should be rejected with 413
    eleven_bytes = b"01234567890"
    resp_big = client.post("/echo-bytes", data=eleven_bytes, headers={"Content-Length": str(len(eleven_bytes))})
    assert resp_big.status_code == 413


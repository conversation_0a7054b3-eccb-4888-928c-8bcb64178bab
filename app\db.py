from typing import Optional, List
from datetime import datetime
import asyncio
import certifi
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from bson import ObjectId
from .config import settings
from .security import hash_password


_client: Optional[AsyncIOMotorClient] = None
_db: Optional[AsyncIOMotorDatabase] = None


def get_db() -> AsyncIOMotorDatabase:
    global _db
    if _db is None:
        raise RuntimeError("Database not initialized. Ensure startup has run.")
    return _db


async def connect_to_mongo() -> None:
    global _client, _db
    uri = settings.MONGODB_URI
    client_kwargs = {"serverSelectionTimeoutMS": 30000}
    lower_uri = uri.lower()
    if uri.startswith("mongodb+srv://") or "tls=true" in lower_uri or "ssl=true" in lower_uri:
        client_kwargs["tlsCAFile"] = certifi.where()
        if settings.TLS_ALLOW_INVALID_CERTS:
            client_kwargs["tlsAllowInvalidCertificates"] = True
    _client = AsyncIOMotorClient(uri, **client_kwargs)
    _db = _client[settings.DB_NAME]
    await ensure_indexes(_db)
    if settings.SEED_DEMO:
        await seed_demo(_db)


async def close_mongo_connection() -> None:
    global _client
    if _client is not None:
        _client.close()
        _client = None


async def ensure_indexes(db: AsyncIOMotorDatabase) -> None:
    await db.users.create_index("email", unique=True)
    await db.clients.create_index("name")
    await db.sessions.create_index("client_id")
    await db.progress.create_index([("client_id", 1), ("date", 1)])
    await db.user_notes.create_index([("user_id", 1), ("created_at", -1)])


async def seed_demo(db: AsyncIOMotorDatabase) -> None:
    # Check if users already exist
    existing = await db.users.count_documents({})
    if existing > 0:
        return

    # Create demo users
    parent_email = "<EMAIL>"
    therapist_email = "<EMAIL>"
    admin_email = "<EMAIL>"
    parent_hash, parent_salt = hash_password("parentpass")
    therapist_hash, therapist_salt = hash_password("therapistpass")
    admin_hash, admin_salt = hash_password("adminpass")

    parent_id = ObjectId()
    therapist_id = ObjectId()
    admin_id = ObjectId()
    client_id = ObjectId()

    await db.users.insert_many([
        {
            "_id": parent_id,
            "email": parent_email,
            "full_name": "Pat Parent",
            "role": "parent",
            "password_hash": parent_hash,
            "salt": parent_salt,
            "client_ids": [client_id],
            "created_at": datetime.now(),
        },
        {
            "_id": therapist_id,
            "email": therapist_email,
            "full_name": "Terry Therapist",
            "role": "therapist",
            "password_hash": therapist_hash,
            "salt": therapist_salt,
            "client_ids": [],
            "created_at": datetime.now(),
        },
        {
            "_id": admin_id,
            "email": admin_email,
            "full_name": "Admin User",
            "role": "admin",
            "password_hash": admin_hash,
            "salt": admin_salt,
            "client_ids": [],
            "created_at": datetime.now(),
        },
    ])

    await db.clients.insert_one(
        {
            "_id": client_id,
            "name": "Kid One",
            "dob": datetime(2019, 5, 20),
            "therapist_id": therapist_id,
            "parent_ids": [parent_id],
            "workflow_step": 1,
            "workflow_label": "Intake",
        }
    )

    # Insert a couple of sessions
    await db.sessions.insert_many([
        {
            "client_id": client_id,
            "date": datetime(2025, 1, 10, 10, 0, 0),
            "created_by": therapist_id,
            "notes": "Worked on communication goals.",
            "goals": ["Eye contact 5s", "Requesting items"],
            "metrics": {"eye_contact_seconds": 35, "requests_made": 7},
        },
        {
            "client_id": client_id,
            "date": datetime(2025, 1, 17, 10, 0, 0),
            "created_by": therapist_id,
            "notes": "Improvement observed in requesting.",
            "goals": ["Requesting items", "Following 1-step commands"],
            "metrics": {"eye_contact_seconds": 42, "requests_made": 12},
        },
    ])

    # Insert progress points
    await db.progress.insert_many([
        {"client_id": client_id, "date": datetime(2025, 1, 10), "metrics": {"eye_contact_seconds": 35, "requests_made": 7}},
        {"client_id": client_id, "date": datetime(2025, 1, 17), "metrics": {"eye_contact_seconds": 42, "requests_made": 12}},
        {"client_id": client_id, "date": datetime(2025, 1, 24), "metrics": {"eye_contact_seconds": 46, "requests_made": 14}},
    ])

from fastapi import FastAPI
from fastapi.testclient import TestClient
from bson import ObjectId
import types

# Import routers and modules to monkeypatch
from app.routers import admin as admin_router
from app.routers import users as users_router
from app.routers import clients as clients_router
from app.dependencies import get_current_user as real_get_current_user


class FakeInsertOneResult:
    def __init__(self, inserted_id):
        self.inserted_id = inserted_id


class FakeCursor:
    def __init__(self, docs):
        self._docs = list(docs)
        self._sort = None

    def sort(self, key, direction):
        reverse = direction < 0
        self._docs.sort(key=lambda d: d.get(key), reverse=reverse)
        return self

    async def to_list(self, n):
        return list(self._docs)[:n]


class FakeCollection:
    def __init__(self, initial=None):
        self.docs = initial or []

    async def find_one(self, filter):
        for d in self.docs:
            ok = True
            for k, v in filter.items():
                if d.get(k) != v:
                    ok = False
                    break
            if ok:
                return d
        return None

    def find(self, filter=None, projection=None):
        filter = filter or {}
        matches = []
        for d in self.docs:
            ok = True
            for k, v in filter.items():
                if isinstance(v, dict) and "$in" in v:
                    if d.get(k) not in v["$in"]:
                        ok = False
                        break
                else:
                    if d.get(k) != v:
                        ok = False
                        break
            if ok:
                matches.append(d)
        return FakeCursor(matches)

    async def insert_one(self, doc):
        if "_id" not in doc:
            doc["_id"] = ObjectId()
        self.docs.append(doc)
        return FakeInsertOneResult(doc["_id"])

    async def update_one(self, filter, update):
        doc = await self.find_one(filter)
        if not doc:
            return
        if "$set" in update:
            for k, v in update["$set"].items():
                doc[k] = v

    async def delete_one(self, filter):
        for i, d in enumerate(self.docs):
            ok = True
            for k, v in filter.items():
                if d.get(k) != v:
                    ok = False
                    break
            if ok:
                self.docs.pop(i)
                return


class FakeDB:
    def __init__(self):
        self.users = FakeCollection()
        self.clients = FakeCollection()
        self.user_notes = FakeCollection()


def build_app_with_fakes(fake_db: FakeDB) -> TestClient:
    app = FastAPI()
    app.include_router(admin_router.router)
    app.include_router(users_router.router)
    app.include_router(clients_router.router)

    # Monkeypatch get_db in each router module to return our fake
    admin_router.get_db = lambda: fake_db
    users_router.get_db = lambda: fake_db
    clients_router.get_db = lambda: fake_db

    client = TestClient(app)
    return app, client


def test_admin_notes_and_user_read_only():
    fake_db = FakeDB()
    # Seed users
    user_id = ObjectId()
    admin_id = ObjectId()
    fake_db.users.docs.extend([
        {"_id": user_id, "email": "<EMAIL>", "role": "parent"},
        {"_id": admin_id, "email": "<EMAIL>", "role": "admin"},
    ])

    app, client = build_app_with_fakes(fake_db)

    # Override get_current_user to be admin for admin endpoints
    def admin_user_override():
        return {"_id": admin_id, "email": "<EMAIL>", "role": "admin"}

    from app import dependencies as deps
    app.dependency_overrides[deps.get_current_user] = admin_user_override

    # Create an admin note for the user
    resp = client.post(f"/admin/users/{str(user_id)}/notes", json={"body": "Welcome call Friday"})
    assert resp.status_code == 200, resp.text
    data = resp.json()
    assert data["body"] == "Welcome call Friday"
    assert data["user_id"] == str(user_id)

    # List admin notes for that user
    resp = client.get(f"/admin/users/{str(user_id)}/notes")
    assert resp.status_code == 200
    notes = resp.json()
    assert len(notes) == 1
    assert notes[0]["body"] == "Welcome call Friday"

    # Now override to be the regular user and fetch read-only notes
    def regular_user_override():
        return {"_id": user_id, "email": "<EMAIL>", "role": "parent", "client_ids": []}

    app.dependency_overrides[deps.get_current_user] = regular_user_override

    resp = client.get("/users/me/notes")
    assert resp.status_code == 200
    user_notes = resp.json()
    assert len(user_notes) == 1
    assert user_notes[0]["body"] == "Welcome call Friday"
    # Ensure read-only shape: created_by and user_id are not present
    assert "created_by" not in user_notes[0]
    assert "user_id" not in user_notes[0]


def test_admin_sets_workflow_and_therapist_reads_client():
    fake_db = FakeDB()
    # Seed users and client
    therapist_id = ObjectId()
    admin_id = ObjectId()
    client_id = ObjectId()

    fake_db.users.docs.extend([
        {"_id": therapist_id, "email": "<EMAIL>", "role": "therapist"},
        {"_id": admin_id, "email": "<EMAIL>", "role": "admin"},
    ])

    fake_db.clients.docs.append({
        "_id": client_id,
        "name": "Child",
        "therapist_id": therapist_id,
        "parent_ids": [],
    })

    app, client = build_app_with_fakes(fake_db)
    from app import dependencies as deps

    # Admin sets workflow
    def admin_user_override():
        return {"_id": admin_id, "email": "<EMAIL>", "role": "admin"}

    app.dependency_overrides[deps.get_current_user] = admin_user_override
    resp = client.patch(f"/admin/clients/{str(client_id)}/workflow", json={"workflow_step": 3, "workflow_label": "Assessment"})
    assert resp.status_code == 200, resp.text
    updated = resp.json()
    assert updated["workflow_step"] == 3
    assert updated["workflow_label"] == "Assessment"

    # Therapist reads client and sees workflow fields
    def therapist_override():
        return {"_id": therapist_id, "email": "<EMAIL>", "role": "therapist"}

    app.dependency_overrides[deps.get_current_user] = therapist_override
    resp = client.get(f"/clients/{str(client_id)}")
    assert resp.status_code == 200
    c = resp.json()
    assert c["workflow_step"] == 3
    assert c["workflow_label"] == "Assessment"


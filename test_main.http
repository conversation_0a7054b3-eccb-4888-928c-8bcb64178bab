# ABA Client Portal API - Sample Requests

@baseUrl = http://127.0.0.1:8000

### Health
GET {{baseUrl}}/
Accept: application/json

### Auth: Therapist login (demo seed)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "therapistpass"
}

> {% client.global.set("therapistToken", json.access_token); %}

### Therapist: Who am I
GET {{baseUrl}}/auth/me
Authorization: Bearer {{therapistToken}}

### Therapist: List my clients and capture first client id
GET {{baseUrl}}/clients
Authorization: Bearer {{therapistToken}}

> {% if (json.length) { client.global.set("therapistClientId", json[0]._id); } %}

### Therapist: Create a new session for the first client
POST {{baseUrl}}/sessions
Authorization: Bearer {{therapistToken}}
Content-Type: application/json

{
  "client_id": "{{therapistClientId}}",
  "notes": "Session via HTTP file",
  "goals": ["Eye contact", "Requesting"],
  "metrics": {"eye_contact_seconds": 50, "requests_made": 9}
}

> {% client.global.set("lastSessionId", json._id); %}

### Therapist: List sessions
GET {{baseUrl}}/sessions
Authorization: Bearer {{therapistToken}}

### Therapist: Get session by id
GET {{baseUrl}}/sessions/{{lastSessionId}}
Authorization: Bearer {{therapistToken}}

### Therapist: Analytics progress chart
GET {{baseUrl}}/analytics/clients/{{therapistClientId}}/progress-chart
Authorization: Bearer {{therapistToken}}

### Auth: Parent login (demo seed)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "parentpass"
}

> {% client.global.set("parentToken", json.access_token); %}

### Parent: Who am I
GET {{baseUrl}}/auth/me
Authorization: Bearer {{parentToken}}

### Parent: List my clients and capture first client id
GET {{baseUrl}}/clients
Authorization: Bearer {{parentToken}}

> {% if (json.length) { client.global.set("parentClientId", json[0]._id); } %}

### Parent: Get client details
GET {{baseUrl}}/clients/{{parentClientId}}
Authorization: Bearer {{parentToken}}

### Parent: Get client progress
GET {{baseUrl}}/clients/{{parentClientId}}/progress
Authorization: Bearer {{parentToken}}

### Parent: List sessions for my clients
GET {{baseUrl}}/sessions
Authorization: Bearer {{parentToken}}

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from ..db import get_db
from ..dependencies import get_current_user
from ..schemas import UserNoteOut
from ..utils import convert_object_ids

router = APIRouter(prefix="/users", tags=["users"])


@router.get("/me/notes", response_model=List[UserNoteOut])
async def my_notes(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Read-only endpoint for a user to view admin-authored notes attached to their account."""
    db = get_db()
    cursor = db.user_notes.find({"user_id": current_user["_id"]}).sort("created_at", -1)
    docs = await cursor.to_list(1000)
    out = []
    for d in docs:
        safe = convert_object_ids(d)
        out.append({
            "_id": str(d["_id"]),
            "body": safe.get("body"),
            "created_at": safe.get("created_at"),
        })
    return out


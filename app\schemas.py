from typing import Optional, List, Dict, Literal
from datetime import datetime, date
from pydantic import BaseModel, EmailStr


class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"


class UserMe(BaseModel):
    _id: str
    email: EmailStr
    full_name: Optional[str] = None
    role: Literal["parent", "therapist", "admin"]
    client_ids: Optional[List[str]] = None


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


class ClientOut(BaseModel):
    _id: str
    name: str
    dob: Optional[date] = None
    therapist_id: Optional[str] = None
    parent_ids: Optional[List[str]] = None
    workflow_step: Optional[int] = None  # 1-8 indicating progress in the workflow
    workflow_label: Optional[str] = None


class SessionCreate(BaseModel):
    client_id: str
    date: Optional[datetime] = None
    notes: Optional[str] = None
    goals: Optional[List[str]] = None
    metrics: Optional[Dict[str, float | int]] = None


class SessionOut(BaseModel):
    _id: str
    client_id: str
    date: datetime
    created_by: str
    notes: Optional[str] = None
    goals: Optional[List[str]] = None
    metrics: Optional[Dict[str, float | int]] = None


class ProgressPoint(BaseModel):
    date: date
    metrics: Dict[str, float | int]


class ProgressSeriesResponse(BaseModel):
    client_id: str
    points: List[ProgressPoint]


class ProgressChartResponse(BaseModel):
    client_id: str
    dates: List[date]
    series: Dict[str, List[float]]


# Admin schemas
class UserAdmin(BaseModel):
    _id: str
    email: EmailStr
    full_name: Optional[str] = None
    role: Literal["parent", "therapist", "admin"]
    client_ids: Optional[List[str]] = None
    created_at: Optional[datetime] = None


class UserCreate(BaseModel):
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    role: Literal["parent", "therapist", "admin"]
    client_ids: Optional[List[str]] = None


class PasswordChange(BaseModel):
    new_password: str


# Admin Notes schemas
class AdminNoteCreate(BaseModel):
    body: str

class AdminNoteOut(BaseModel):
    _id: str
    user_id: str
    body: str
    created_at: datetime
    created_by: str


# User-facing: read-only notes
class UserNoteOut(BaseModel):
    _id: str
    body: str
    created_at: datetime


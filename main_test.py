from fastapi import FastAPI

# Simple test version without database dependency
app = FastAPI(title="ABA Client Portal API (Test)", version="0.1.0")

# Health/root endpoint
@app.get("/")
async def root():
    return {"message": "ABA Client Portal API is running (test mode)"}

@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}

# Mock auth endpoint for testing
@app.post("/auth/login")
async def mock_login():
    return {
        "access_token": "mock_token_for_testing",
        "token_type": "bearer"
    }

@app.get("/auth/me")
async def mock_me():
    return {
        "_id": "mock_user_id",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "role": "therapist",
        "client_ids": ["mock_client_id"]
    }

# Mock clients endpoint
@app.get("/clients")
async def mock_clients():
    return [
        {
            "_id": "mock_client_id",
            "name": "Test Client",
            "dob": "2019-05-20",
            "therapist_id": "mock_therapist_id",
            "parent_ids": ["mock_parent_id"]
        }
    ]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_test:app", host="127.0.0.1", port=8000, reload=True)
